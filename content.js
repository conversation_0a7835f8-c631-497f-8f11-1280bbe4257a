// Store user's mute preferences per video
const userMutePreferences = new WeakMap();

// Create custom video controls with high z-index
function createCustomControls(video) {
  const controlsContainer = document.createElement('div');
  controlsContainer.className = 'ig-video-controls';
  controlsContainer.style.cssText = `
    position: absolute;
    bottom: 10px;
    left: 10px;
    right: 10px;
    background: rgba(0, 0, 0, 0.7);
    border-radius: 8px;
    padding: 8px;
    display: flex;
    align-items: center;
    gap: 10px;
    z-index: 999999 !important;
    pointer-events: auto;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    font-size: 14px;
    color: white;
  `;

  // Play/Pause button
  const playPauseBtn = document.createElement('button');
  playPauseBtn.innerHTML = video.paused ? '▶️' : '⏸️';
  playPauseBtn.style.cssText = `
    background: none;
    border: none;
    color: white;
    font-size: 16px;
    cursor: pointer;
    padding: 4px;
    border-radius: 4px;
  `;
  playPauseBtn.addEventListener('click', (e) => {
    e.stopPropagation();
    if (video.paused) {
      // Restore user's mute preference when playing
      const userMutePreference = userMutePreferences.get(video);
      if (userMutePreference !== undefined) {
        video.muted = userMutePreference;
      }
      video.play();
      playPauseBtn.innerHTML = '⏸️';
    } else {
      video.pause();
      playPauseBtn.innerHTML = '▶️';
    }
  });

  // Progress bar
  const progressContainer = document.createElement('div');
  progressContainer.style.cssText = `
    flex: 1;
    height: 20px;
    background: transparent;
    border-radius: 10px;
    cursor: pointer;
    position: relative;
    display: flex;
    align-items: center;
    padding: 8px 0;
  `;

  // Progress track (the background line)
  const progressTrack = document.createElement('div');
  progressTrack.style.cssText = `
    width: 100%;
    height: 4px;
    background: rgba(255, 255, 255, 0.3);
    border-radius: 2px;
    position: relative;
  `;

  const progressBar = document.createElement('div');
  progressBar.style.cssText = `
    height: 100%;
    background: #1877f2;
    border-radius: 2px;
    width: 0%;
    transition: width 0.1s;
    position: relative;
  `;

  // Progress thumb (draggable handle)
  const progressThumb = document.createElement('div');
  progressThumb.style.cssText = `
    position: absolute;
    right: -8px;
    top: 50%;
    transform: translateY(-50%);
    width: 16px;
    height: 16px;
    background: #1877f2;
    border: 2px solid white;
    border-radius: 50%;
    cursor: grab;
    opacity: 0;
    transition: opacity 0.2s;
    box-shadow: 0 2px 4px rgba(0,0,0,0.3);
  `;

  progressBar.appendChild(progressThumb);
  progressTrack.appendChild(progressBar);
  progressContainer.appendChild(progressTrack);

  // Show thumb on hover
  progressContainer.addEventListener('mouseenter', () => {
    progressThumb.style.opacity = '1';
  });
  progressContainer.addEventListener('mouseleave', () => {
    progressThumb.style.opacity = '0';
  });

  // Progress bar interaction handlers
  let isDragging = false;
  let wasPlaying = false;

  const updateVideoTime = (e) => {
    const rect = progressTrack.getBoundingClientRect();
    const clickX = e.clientX - rect.left;
    const percentage = Math.max(0, Math.min(1, clickX / rect.width));
    video.currentTime = percentage * video.duration;
  };

  // Click handler for quick seeking
  progressContainer.addEventListener('click', (e) => {
    e.stopPropagation();
    if (!isDragging) {
      updateVideoTime(e);
    }
  });

  // Drag functionality
  const startDrag = (e) => {
    e.stopPropagation();
    e.preventDefault();
    isDragging = true;
    wasPlaying = !video.paused;
    if (wasPlaying) {
      video.pause();
    }
    progressThumb.style.cursor = 'grabbing';
    progressThumb.style.opacity = '1';

    updateVideoTime(e);
  };

  const drag = (e) => {
    if (!isDragging) return;
    e.preventDefault();
    updateVideoTime(e);
  };

  const endDrag = (e) => {
    if (!isDragging) return;
    e.preventDefault();
    isDragging = false;
    progressThumb.style.cursor = 'grab';

    if (wasPlaying) {
      video.play();
    }
  };

  // Mouse events
  progressThumb.addEventListener('mousedown', startDrag);
  progressContainer.addEventListener('mousedown', startDrag);
  document.addEventListener('mousemove', drag);
  document.addEventListener('mouseup', endDrag);

  // Touch events for mobile
  progressThumb.addEventListener('touchstart', (e) => {
    e.touches[0] && startDrag(e.touches[0]);
  });
  progressContainer.addEventListener('touchstart', (e) => {
    e.touches[0] && startDrag(e.touches[0]);
  });
  document.addEventListener('touchmove', (e) => {
    e.touches[0] && drag(e.touches[0]);
  });
  document.addEventListener('touchend', endDrag);

  // Speed control
  const speedSelect = document.createElement('select');
  speedSelect.style.cssText = `
    background: rgba(255, 255, 255, 0.2);
    border: none;
    color: white;
    border-radius: 4px;
    padding: 2px 4px;
    font-size: 12px;
    cursor: pointer;
  `;

  const speeds = [0.25, 0.5, 0.75, 1, 1.25, 1.5, 1.75, 2];
  speeds.forEach(speed => {
    const option = document.createElement('option');
    option.value = speed;
    option.textContent = speed === 1 ? 'Normal' : `${speed}x`;
    option.style.background = '#000';
    if (speed === 1) option.selected = true;
    speedSelect.appendChild(option);
  });

  speedSelect.addEventListener('change', (e) => {
    e.stopPropagation();
    video.playbackRate = parseFloat(e.target.value);
  });

  // Time display
  const timeDisplay = document.createElement('span');
  timeDisplay.style.cssText = `
    font-size: 12px;
    color: rgba(255, 255, 255, 0.8);
    min-width: 80px;
    text-align: center;
  `;

  // Mute/Unmute button
  const muteBtn = document.createElement('button');
  // We'll set the icon after we initialize the mute state
  muteBtn.style.cssText = `
    background: none;
    border: none;
    color: white;
    font-size: 14px;
    cursor: pointer;
    padding: 4px;
    border-radius: 4px;
  `;
  muteBtn.addEventListener('click', (e) => {
    e.stopPropagation();
    video.muted = !video.muted;
    // Store user's mute preference
    userMutePreferences.set(video, video.muted);
    muteBtn.innerHTML = video.muted ? '🔇' : '🔊';
  });

  // Fullscreen button
  const fullscreenBtn = document.createElement('button');
  fullscreenBtn.innerHTML = '⛶';
  fullscreenBtn.style.cssText = `
    background: none;
    border: none;
    color: white;
    font-size: 14px;
    cursor: pointer;
    padding: 4px;
    border-radius: 4px;
  `;
  fullscreenBtn.addEventListener('click', (e) => {
    e.stopPropagation();
    if (video.requestFullscreen) {
      video.requestFullscreen();
    } else if (video.webkitRequestFullscreen) {
      video.webkitRequestFullscreen();
    }
  });

  // Update progress and time
  const updateProgress = () => {
    if (video.duration) {
      const percentage = (video.currentTime / video.duration) * 100;
      progressBar.style.width = `${percentage}%`;

      const formatTime = (seconds) => {
        const mins = Math.floor(seconds / 60);
        const secs = Math.floor(seconds % 60);
        return `${mins}:${secs.toString().padStart(2, '0')}`;
      };

      timeDisplay.textContent = `${formatTime(video.currentTime)} / ${formatTime(video.duration)}`;
    }
  };

  video.addEventListener('timeupdate', updateProgress);
  video.addEventListener('loadedmetadata', updateProgress);

  // Update play/pause button when video state changes
  video.addEventListener('play', () => playPauseBtn.innerHTML = '⏸️');
  video.addEventListener('pause', () => playPauseBtn.innerHTML = '▶️');

  // Update mute button when video muted state changes
  video.addEventListener('volumechange', () => {
    muteBtn.innerHTML = video.muted ? '🔇' : '🔊';
    // Sync our preference with any external mute changes (like native controls)
    userMutePreferences.set(video, video.muted);
  });

  // Assemble controls
  controlsContainer.appendChild(playPauseBtn);
  controlsContainer.appendChild(progressContainer);
  controlsContainer.appendChild(timeDisplay);
  controlsContainer.appendChild(muteBtn);
  controlsContainer.appendChild(speedSelect);
  controlsContainer.appendChild(fullscreenBtn);

  // Set initial mute button state (will be updated after video mute state is set)
  muteBtn.innerHTML = video.muted ? '🔇' : '🔊';

  return controlsContainer;
}

// Function to add controls to a video element
function addControls(video) {
  if (video.hasAttribute('data-controls-added')) {
    return;
  }
  video.setAttribute('data-controls-added', 'true');

  // Initialize user mute preference if not set
  // Instagram videos start muted by default, but we want to remember user preference
  if (!userMutePreferences.has(video)) {
    // Set initial preference to false (unmuted) to override Instagram's default
    userMutePreferences.set(video, false);
    // Actually unmute the video to override Instagram's default
    // Use a small delay to ensure this happens after Instagram's initialization
    setTimeout(() => {
      video.muted = false;
    }, 50);
  } else {
    // Apply stored preference
    const storedMuteState = userMutePreferences.get(video);
    setTimeout(() => {
      video.muted = storedMuteState;
    }, 50);
  }

  // Remove default controls to avoid conflicts
  video.controls = false;



  // Find the video container (usually the parent element)
  const videoContainer = video.parentElement;
  if (!videoContainer) return;

  // Make sure the container has relative positioning
  const containerStyle = window.getComputedStyle(videoContainer);
  if (containerStyle.position === 'static') {
    videoContainer.style.position = 'relative';
  }

  // Create and add custom controls
  const customControls = createCustomControls(video);
  videoContainer.appendChild(customControls);

  // Hide controls on mouse leave, show on mouse enter
  let hideTimeout;
  const showControls = () => {
    customControls.style.opacity = '1';
    clearTimeout(hideTimeout);
  };

  const hideControls = () => {
    hideTimeout = setTimeout(() => {
      customControls.style.opacity = '0';
    }, 2000);
  };

  customControls.style.transition = 'opacity 0.3s';
  videoContainer.addEventListener('mouseenter', showControls);
  videoContainer.addEventListener('mouseleave', hideControls);
  videoContainer.addEventListener('mousemove', showControls);

  // Initially hide controls after 3 seconds
  hideTimeout = setTimeout(() => {
    customControls.style.opacity = '0';
  }, 3000);
}

// Function to find and add controls to all videos on the page
function addControlsToAllVideos() {
  const videos = document.querySelectorAll('video');
  videos.forEach(addControls);
}

// Add CSS to ensure our controls take priority and hide conflicting elements
const style = document.createElement('style');
style.textContent = `
  /* Hide Instagram's native video controls */
  video::-webkit-media-controls {
    display: none !important;
  }

  video::-webkit-media-controls-enclosure {
    display: none !important;
  }

  /* Ensure our controls are always on top */
  .ig-video-controls {
    z-index: 999999 !important;
    pointer-events: auto !important;
  }

  /* Hide any conflicting Instagram control elements */
  [role="button"][aria-label*="mute"],
  [role="button"][aria-label*="unmute"],
  [role="button"][aria-label*="volume"] {
    display: none !important;
  }
`;
document.head.appendChild(style);

// Run the function when the script is first injected
addControlsToAllVideos();

// Observe the page for new videos being added (e.g., when scrolling)
const observer = new MutationObserver((mutations) => {
  mutations.forEach((mutation) => {
    if (mutation.addedNodes) {
      mutation.addedNodes.forEach((node) => {
        if (node.nodeName === 'VIDEO') {
          addControls(node);
        } else if (node.querySelectorAll) {
          const videos = node.querySelectorAll('video');
          videos.forEach(addControls);
        }
      });
    }
  });
});

// Start observing the entire document
observer.observe(document.body, {
  childList: true,
  subtree: true,
});

// Keyboard shortcuts for video control
document.addEventListener('keydown', (e) => {
  // Only trigger if we're not typing in an input field
  if (e.target.tagName === 'INPUT' || e.target.tagName === 'TEXTAREA' || e.target.isContentEditable) {
    return;
  }

  // Find the currently visible video (the one in viewport)
  const videos = document.querySelectorAll('video[data-controls-added]');
  let activeVideo = null;

  // Find the video that's most visible in the viewport
  videos.forEach(video => {
    const rect = video.getBoundingClientRect();
    const isVisible = rect.top < window.innerHeight && rect.bottom > 0 &&
                     rect.left < window.innerWidth && rect.right > 0;

    if (isVisible) {
      const visibleHeight = Math.min(rect.bottom, window.innerHeight) - Math.max(rect.top, 0);
      const visibleWidth = Math.min(rect.right, window.innerWidth) - Math.max(rect.left, 0);
      const visibleArea = visibleHeight * visibleWidth;
      const totalArea = rect.height * rect.width;
      const visibilityRatio = visibleArea / totalArea;

      if (visibilityRatio > 0.5) {
        activeVideo = video;
      }
    }
  });

  if (!activeVideo) return;

  const key = e.key.toLowerCase();

  if (key === 'd') {
    // Increase speed (D for faster)
    e.preventDefault();
    changeVideoSpeed(activeVideo, 1);
  } else if (key === 's') {
    // Decrease speed (S for slower)
    e.preventDefault();
    changeVideoSpeed(activeVideo, -1);
  } else if (key === 'a') {
    // Reset to normal speed (A for auto/normal)
    e.preventDefault();
    setVideoSpeedToNormal(activeVideo);
  } else if (key === 'm') {
    // Toggle mute (M for mute)
    e.preventDefault();
    toggleVideoMute(activeVideo);
  } else if (key === 'r') {
    // Restart video (R for restart)
    e.preventDefault();
    restartVideo(activeVideo);
  }
});

// Function to change video speed
function changeVideoSpeed(video, direction) {
  const speeds = [0.25, 0.5, 0.75, 1, 1.25, 1.5, 1.75, 2];
  const currentSpeed = video.playbackRate;
  let currentIndex = speeds.findIndex(speed => Math.abs(speed - currentSpeed) < 0.01);

  // If current speed is not in our array, find the closest one
  if (currentIndex === -1) {
    currentIndex = speeds.reduce((closest, speed, index) => {
      return Math.abs(speed - currentSpeed) < Math.abs(speeds[closest] - currentSpeed) ? index : closest;
    }, 0);
  }

  let newIndex;
  if (direction > 0) {
    newIndex = Math.min(currentIndex + 1, speeds.length - 1);
  } else {
    newIndex = Math.max(currentIndex - 1, 0);
  }

  video.playbackRate = speeds[newIndex];

  // Update the speed select dropdown if it exists
  const container = video.parentElement;
  const speedSelect = container?.querySelector('select');
  if (speedSelect) {
    speedSelect.value = speeds[newIndex];
  }

  // Show notification
  const speedText = speeds[newIndex] === 1 ? 'Normal' : `${speeds[newIndex]}x`;
  showNotification(video, speedText);
}

// Function to set video speed to normal (1x)
function setVideoSpeedToNormal(video) {
  video.playbackRate = 1;

  // Update the speed select dropdown if it exists
  const container = video.parentElement;
  const speedSelect = container?.querySelector('select');
  if (speedSelect) {
    speedSelect.value = 1;
  }

  // Show notification
  showNotification(video, 'Normal');
}

// Function to toggle video mute
function toggleVideoMute(video) {
  video.muted = !video.muted;
  // Store user's mute preference
  userMutePreferences.set(video, video.muted);

  // Update the mute button if it exists
  const container = video.parentElement;
  // Find the mute button (it will have the speaker emoji)
  const buttons = container?.querySelectorAll('button');
  buttons?.forEach(btn => {
    if (btn.innerHTML === '🔊' || btn.innerHTML === '🔇') {
      btn.innerHTML = video.muted ? '🔇' : '🔊';
    }
  });

  // Show notification
  showNotification(video, video.muted ? 'Muted' : 'Unmuted');
}

// Function to restart video from beginning
function restartVideo(video) {
  video.currentTime = 0;

  // Show notification
  showNotification(video, 'Restarted');
}

// Function to show notifications
function showNotification(video, text) {
  const container = video.parentElement;
  if (!container) return;

  // Remove any existing notification
  const existingNotification = container.querySelector('.video-notification');
  if (existingNotification) {
    existingNotification.remove();
  }

  // Create notification element
  const notification = document.createElement('div');
  notification.className = 'video-notification';
  notification.textContent = text;
  notification.style.cssText = `
    position: absolute;
    top: 20px;
    right: 20px;
    background: rgba(0, 0, 0, 0.8);
    color: white;
    padding: 8px 12px;
    border-radius: 6px;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    font-size: 14px;
    font-weight: bold;
    z-index: 999999;
    pointer-events: none;
    opacity: 1;
    transition: opacity 0.3s ease;
  `;

  container.appendChild(notification);

  // Fade out and remove after 1.5 seconds
  setTimeout(() => {
    notification.style.opacity = '0';
    setTimeout(() => {
      if (notification.parentElement) {
        notification.remove();
      }
    }, 300);
  }, 1500);
}

// Keyboard shortcuts for speed control
document.addEventListener('keydown', (e) => {
  // Only trigger if we're not typing in an input field
  if (e.target.tagName === 'INPUT' || e.target.tagName === 'TEXTAREA' || e.target.isContentEditable) {
    return;
  }

  // Find the currently visible video (the one in viewport)
  const videos = document.querySelectorAll('video[data-controls-added]');
  let activeVideo = null;

  // Find the video that's most visible in the viewport
  videos.forEach(video => {
    const rect = video.getBoundingClientRect();
    const isVisible = rect.top < window.innerHeight && rect.bottom > 0 &&
                     rect.left < window.innerWidth && rect.right > 0;

    if (isVisible) {
      // Calculate how much of the video is visible
      const visibleHeight = Math.min(rect.bottom, window.innerHeight) - Math.max(rect.top, 0);
      const visibleWidth = Math.min(rect.right, window.innerWidth) - Math.max(rect.left, 0);
      const visibleArea = visibleHeight * visibleWidth;
      const totalArea = rect.height * rect.width;
      const visibilityRatio = visibleArea / totalArea;

      // Use the video that's most visible (at least 50% visible)
      if (visibilityRatio > 0.5) {
        activeVideo = video;
      }
    }
  });

  if (!activeVideo) return;

  const speeds = [0.25, 0.5, 0.75, 1, 1.25, 1.5, 1.75, 2];
  const currentSpeed = activeVideo.playbackRate;
  const currentIndex = speeds.findIndex(speed => Math.abs(speed - currentSpeed) < 0.01);

  if (e.key.toLowerCase() === 'd') {
    // Increase speed (D for faster)
    e.preventDefault();
    const nextIndex = Math.min(currentIndex + 1, speeds.length - 1);
    activeVideo.playbackRate = speeds[nextIndex];

    // Update the speed select dropdown if it exists
    const container = activeVideo.parentElement;
    const speedSelect = container?.querySelector('select');
    if (speedSelect) {
      speedSelect.value = speeds[nextIndex];
    }

    // Show a brief notification
    showSpeedNotification(activeVideo, `${speeds[nextIndex]}x`);

  } else if (e.key.toLowerCase() === 's') {
    // Decrease speed (S for slower)
    e.preventDefault();
    const prevIndex = Math.max(currentIndex - 1, 0);
    activeVideo.playbackRate = speeds[prevIndex];

    // Update the speed select dropdown if it exists
    const container = activeVideo.parentElement;
    const speedSelect = container?.querySelector('select');
    if (speedSelect) {
      speedSelect.value = speeds[prevIndex];
    }

    // Show a brief notification
    showSpeedNotification(activeVideo, `${speeds[prevIndex]}x`);
  }
});

// Function to show speed change notification
function showSpeedNotification(video, speedText) {
  const container = video.parentElement;
  if (!container) return;

  // Remove any existing notification
  const existingNotification = container.querySelector('.speed-notification');
  if (existingNotification) {
    existingNotification.remove();
  }

  // Create notification element
  const notification = document.createElement('div');
  notification.className = 'speed-notification';
  notification.textContent = speedText;
  notification.style.cssText = `
    position: absolute;
    top: 20px;
    right: 20px;
    background: rgba(0, 0, 0, 0.8);
    color: white;
    padding: 8px 12px;
    border-radius: 6px;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    font-size: 14px;
    font-weight: bold;
    z-index: 999999;
    pointer-events: none;
    opacity: 1;
    transition: opacity 0.3s ease;
  `;

  container.appendChild(notification);

  // Fade out and remove after 1.5 seconds
  setTimeout(() => {
    notification.style.opacity = '0';
    setTimeout(() => {
      if (notification.parentElement) {
        notification.remove();
      }
    }, 300);
  }, 1500);
}

<!DOCTYPE html>
<html>
<head>
    <title>Test Instagram Video Controls</title>
    <style>
        body {
            margin: 0;
            padding: 20px;
            background: #000;
            color: white;
            font-family: Arial, sans-serif;
        }
        .video-container {
            position: relative;
            width: 400px;
            height: 600px;
            margin: 20px auto;
            background: #333;
            border-radius: 10px;
            overflow: hidden;
        }
        video {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }
        .overlay-content {
            position: absolute;
            bottom: 60px;
            left: 20px;
            right: 20px;
            z-index: 1000;
            background: rgba(0,0,0,0.5);
            padding: 10px;
            border-radius: 5px;
        }
        .username {
            font-weight: bold;
            margin-bottom: 5px;
        }
        .description {
            font-size: 14px;
            opacity: 0.8;
        }
    </style>
</head>
<body>
    <h1>Test Page for Instagram Video Controls Extension</h1>
    <p>This page simulates Instagram's video layout to test the extension.</p>
    
    <div class="video-container">
        <video loop muted autoplay>
            <source src="https://sample-videos.com/zip/10/mp4/SampleVideo_1280x720_1mb.mp4" type="video/mp4">
            Your browser does not support the video tag.
        </video>
        
        <!-- Simulating Instagram's overlay content that was covering the controls -->
        <div class="overlay-content">
            <div class="username">@testuser</div>
            <div class="description">This is a test video description that would normally cover the video controls...</div>
        </div>
    </div>

    <script src="content.js"></script>
</body>
</html>
